package follow

import (
	"github.com/gin-gonic/gin"
	"pxpat-backend/internal/user-cluster/user-service/external/handler"
)

// RegisterFollowExternalRoutes 注册关注功能的外部路由
func RegisterFollowExternalRoutes(r *gin.RouterGroup, followHandler *handler.FollowHandler, authMiddleware gin.HandlerFunc) {
	// 关注相关路由组
	followGroup := r.Group("/follow")

	// 需要认证的路由
	followGroup.Use(authMiddleware)
	{
		// 关注用户
		followGroup.POST("/user", followHandler.FollowUser)

		// 取消关注用户
		followGroup.DELETE("/user", followHandler.UnfollowUser)

		// 检查关注状态
		followGroup.GET("/status", followHandler.CheckFollowStatus)

		// 批量检查关注状态
		followGroup.POST("/status/batch", followHandler.BatchCheckFollowStatus)

		// 获取关注统计信息
		followGroup.GET("/stats", followHandler.GetFollowStats)
	}

	// 粉丝和关注列表路由组
	followListGroup := r.Group("/users")
	needLoginFollowListGroup := followListGroup.Group("")
	{
		// 公开路由（无需认证）- 可以查看任何用户的粉丝和关注列表
		needLoginFollowListGroup.GET("/followers", followHandler.GetFollowers)
		needLoginFollowListGroup.GET("/following", followHandler.GetFollowing)
	}
}
