{"openapi": "3.0.3", "info": {"title": "关注功能 API", "description": "用户关注功能的完整API接口文档，包括关注、取消关注、获取粉丝列表、获取关注列表等功能", "version": "1.0.0", "contact": {"name": "PXPAT Backend Team", "email": "<EMAIL>"}}, "servers": [{"url": "https://api.pxpat.com", "description": "生产环境"}, {"url": "https://api-dev.pxpat.com", "description": "开发环境"}], "paths": {"/api/follow/user": {"post": {"tags": ["关注管理"], "summary": "关注用户", "description": "关注指定用户", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FollowUserRequest"}}}}, "responses": {"200": {"description": "关注成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/GlobalResponse"}, {"properties": {"data": {"$ref": "#/components/schemas/FollowUserResponse"}}}]}}}}, "400": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "401": {"description": "未授权", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "500": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "delete": {"tags": ["关注管理"], "summary": "取消关注用户", "description": "取消关注指定用户", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UnfollowUserRequest"}}}}, "responses": {"200": {"description": "取消关注成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/GlobalResponse"}, {"properties": {"data": {"$ref": "#/components/schemas/FollowUserResponse"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/api/follow/status": {"get": {"tags": ["关注管理"], "summary": "检查关注状态", "description": "检查当前用户与指定用户的关注状态", "security": [{"BearerAuth": []}], "parameters": [{"name": "followee_ksuid", "in": "query", "required": true, "description": "被关注者的用户KSUID", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取关注状态成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/GlobalResponse"}, {"properties": {"data": {"$ref": "#/components/schemas/CheckFollowStatusResponse"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/api/follow/status/batch": {"post": {"tags": ["关注管理"], "summary": "批量检查关注状态", "description": "批量检查当前用户与多个用户的关注状态", "security": [{"BearerAuth": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BatchCheckFollowRequest"}}}}, "responses": {"200": {"description": "批量检查关注状态成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/GlobalResponse"}, {"properties": {"data": {"$ref": "#/components/schemas/BatchCheckFollowResponse"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/api/follow/stats": {"get": {"tags": ["关注管理"], "summary": "获取关注统计", "description": "获取指定用户或当前用户的关注统计信息", "security": [{"BearerAuth": []}], "parameters": [{"name": "user_ksuid", "in": "query", "required": false, "description": "用户KSUID，如果为空则获取当前用户的统计", "schema": {"type": "string"}}], "responses": {"200": {"description": "获取关注统计成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/GlobalResponse"}, {"properties": {"data": {"$ref": "#/components/schemas/FollowStatsResponse"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/api/users/followers": {"get": {"tags": ["粉丝和关注列表"], "summary": "获取粉丝列表", "description": "获取指定用户的粉丝列表，支持分页查询", "parameters": [{"name": "user_ksuid", "in": "query", "required": false, "description": "用户KSUID，如果为空则需要认证获取当前用户的粉丝", "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": true, "description": "页码，从1开始", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "page_size", "in": "query", "required": true, "description": "每页数量，最大100", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}], "responses": {"200": {"description": "获取粉丝列表成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/GlobalResponse"}, {"properties": {"data": {"$ref": "#/components/schemas/FollowListResponse"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/api/users/following": {"get": {"tags": ["粉丝和关注列表"], "summary": "获取关注列表", "description": "获取指定用户的关注列表，支持分页查询", "parameters": [{"name": "user_ksuid", "in": "query", "required": false, "description": "用户KSUID，如果为空则需要认证获取当前用户的关注", "schema": {"type": "string"}}, {"name": "page", "in": "query", "required": true, "description": "页码，从1开始", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "page_size", "in": "query", "required": true, "description": "每页数量，最大100", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}], "responses": {"200": {"description": "获取关注列表成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/GlobalResponse"}, {"properties": {"data": {"$ref": "#/components/schemas/FollowListResponse"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "403": {"$ref": "#/components/responses/Forbidden"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/api/users/me/followers": {"get": {"tags": ["粉丝和关注列表"], "summary": "获取我的粉丝列表", "description": "获取当前用户的粉丝列表，需要认证", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "required": true, "description": "页码，从1开始", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "page_size", "in": "query", "required": true, "description": "每页数量，最大100", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}], "responses": {"200": {"description": "获取我的粉丝列表成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/GlobalResponse"}, {"properties": {"data": {"$ref": "#/components/schemas/FollowListResponse"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}, "/api/users/me/following": {"get": {"tags": ["粉丝和关注列表"], "summary": "获取我的关注列表", "description": "获取当前用户的关注列表，需要认证", "security": [{"BearerAuth": []}], "parameters": [{"name": "page", "in": "query", "required": true, "description": "页码，从1开始", "schema": {"type": "integer", "minimum": 1, "default": 1}}, {"name": "page_size", "in": "query", "required": true, "description": "每页数量，最大100", "schema": {"type": "integer", "minimum": 1, "maximum": 100, "default": 20}}], "responses": {"200": {"description": "获取我的关注列表成功", "content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/GlobalResponse"}, {"properties": {"data": {"$ref": "#/components/schemas/FollowListResponse"}}}]}}}}, "400": {"$ref": "#/components/responses/BadRequest"}, "401": {"$ref": "#/components/responses/Unauthorized"}, "500": {"$ref": "#/components/responses/InternalServerError"}}}}}, "components": {"securitySchemes": {"BearerAuth": {"type": "http", "scheme": "bearer", "bearerFormat": "JWT", "description": "JWT认证，在请求头中添加 Authorization: Bearer <token>"}}, "responses": {"BadRequest": {"description": "请求参数错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "Unauthorized": {"description": "未授权", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "Forbidden": {"description": "权限不足", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}, "InternalServerError": {"description": "服务器内部错误", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}, "schemas": {"GlobalResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "响应状态码，0表示成功"}, "data": {"type": "object", "description": "响应数据"}}, "required": ["code"]}, "ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer", "description": "错误码"}}, "required": ["code"], "example": {"code": 10000}}, "FollowUserRequest": {"type": "object", "properties": {"followee_ksuid": {"type": "string", "description": "被关注者的用户KSUID"}}, "required": ["followee_ksuid"], "example": {"followee_ksuid": "2Nq7Ej8Ej8Ej8Ej8Ej8Ej8Ej8"}}, "UnfollowUserRequest": {"type": "object", "properties": {"followee_ksuid": {"type": "string", "description": "被关注者的用户KSUID"}}, "required": ["followee_ksuid"], "example": {"followee_ksuid": "2Nq7Ej8Ej8Ej8Ej8Ej8Ej8Ej8"}}, "FollowUserResponse": {"type": "object", "properties": {"success": {"type": "boolean", "description": "操作是否成功"}, "message": {"type": "string", "description": "响应消息"}, "followee_ksuid": {"type": "string", "description": "被关注者的用户KSUID"}, "is_following": {"type": "boolean", "description": "是否已关注"}}, "required": ["success", "message", "followee_ksuid", "is_following"], "example": {"success": true, "message": "关注成功", "followee_ksuid": "2Nq7Ej8Ej8Ej8Ej8Ej8Ej8Ej8", "is_following": true}}, "CheckFollowStatusResponse": {"type": "object", "properties": {"is_following": {"type": "boolean", "description": "是否已关注"}, "is_followed_by": {"type": "boolean", "description": "是否被对方关注"}, "is_mutual_follow": {"type": "boolean", "description": "是否互相关注"}}, "required": ["is_following", "is_followed_by", "is_mutual_follow"], "example": {"is_following": true, "is_followed_by": false, "is_mutual_follow": false}}, "BatchCheckFollowRequest": {"type": "object", "properties": {"user_ksuids": {"type": "array", "items": {"type": "string"}, "maxItems": 100, "description": "用户KSUID列表，最多100个"}}, "required": ["user_ksuids"], "example": {"user_ksuids": ["2Nq7Ej8Ej8Ej8Ej8Ej8Ej8Ej8", "2Nq7Fj8Ej8Ej8Ej8Ej8Ej8Ej8"]}}, "UserFollowStatus": {"type": "object", "properties": {"user_ksuid": {"type": "string", "description": "用户KSUID"}, "is_following": {"type": "boolean", "description": "是否已关注"}, "is_followed_by": {"type": "boolean", "description": "是否被对方关注"}, "is_mutual_follow": {"type": "boolean", "description": "是否互相关注"}}, "required": ["user_ksuid", "is_following", "is_followed_by", "is_mutual_follow"], "example": {"user_ksuid": "2Nq7Ej8Ej8Ej8Ej8Ej8Ej8Ej8", "is_following": true, "is_followed_by": false, "is_mutual_follow": false}}, "BatchCheckFollowResponse": {"type": "object", "properties": {"follow_statuses": {"type": "array", "items": {"$ref": "#/components/schemas/UserFollowStatus"}, "description": "关注状态列表"}}, "required": ["follow_statuses"], "example": {"follow_statuses": [{"user_ksuid": "2Nq7Ej8Ej8Ej8Ej8Ej8Ej8Ej8", "is_following": true, "is_followed_by": false, "is_mutual_follow": false}]}}, "FollowStatsResponse": {"type": "object", "properties": {"following_count": {"type": "integer", "format": "int64", "description": "关注数"}, "followers_count": {"type": "integer", "format": "int64", "description": "粉丝数"}}, "required": ["following_count", "followers_count"], "example": {"following_count": 123, "followers_count": 456}}, "UserFollowInfo": {"type": "object", "properties": {"user_ksuid": {"type": "string", "description": "用户KSUID"}, "username": {"type": "string", "description": "用户名"}, "nickname": {"type": "string", "description": "昵称"}, "avatar_url": {"type": "string", "description": "头像URL"}, "user_type": {"type": "string", "description": "用户类型"}, "is_verified": {"type": "boolean", "description": "是否认证"}, "followed_at": {"type": "string", "format": "date-time", "description": "关注时间"}, "following_count": {"type": "integer", "format": "int64", "description": "关注数"}, "fan_count": {"type": "integer", "description": "粉丝数"}}, "required": ["user_ksuid", "username", "nickname", "avatar_url", "user_type", "is_verified", "followed_at", "following_count", "fan_count"], "example": {"user_ksuid": "2Nq7Ej8Ej8Ej8Ej8Ej8Ej8Ej8", "username": "user123", "nickname": "用户昵称", "avatar_url": "https://example.com/avatar.jpg", "user_type": "normal", "is_verified": true, "followed_at": "2024-01-15T10:30:00Z", "following_count": 100, "fan_count": 200}}, "FollowListResponse": {"type": "object", "properties": {"users": {"type": "array", "items": {"$ref": "#/components/schemas/UserFollowInfo"}, "description": "用户列表"}, "total": {"type": "integer", "format": "int64", "description": "总数"}, "page": {"type": "integer", "description": "当前页码"}, "page_size": {"type": "integer", "description": "每页数量"}, "total_pages": {"type": "integer", "description": "总页数"}}, "required": ["users", "total", "page", "page_size", "total_pages"], "example": {"users": [{"user_ksuid": "2Nq7Ej8Ej8Ej8Ej8Ej8Ej8Ej8", "username": "user123", "nickname": "用户昵称", "avatar_url": "https://example.com/avatar.jpg", "user_type": "normal", "is_verified": true, "followed_at": "2024-01-15T10:30:00Z", "following_count": 100, "fan_count": 200}], "total": 1, "page": 1, "page_size": 20, "total_pages": 1}}}}, "tags": [{"name": "关注管理", "description": "用户关注和取消关注相关操作"}, {"name": "粉丝和关注列表", "description": "获取用户的粉丝列表和关注列表"}]}